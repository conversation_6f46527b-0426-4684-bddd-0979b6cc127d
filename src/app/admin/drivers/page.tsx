'use client'
import React, { useEffect, } from "react";
import {
    Table,
    TableBody,
    TableCell,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { useState, } from 'react';
import { Dialog, DialogBackdrop, DialogPanel, DialogTitle, Field, Label, Radio, RadioGroup, } from '@headlessui/react';

import Tippy from '@tippyjs/react';
import { hideAll } from 'tippy.js';
import 'tippy.js/dist/tippy.css';
import {  PiClockCounterClockwiseLight } from "react-icons/pi";
import { useMutation, useQuery } from "@tanstack/react-query";
import { addTableDriverDetails, deleteDriver, disableDriverQuery, downloadReport, getTableDriverDetails } from "./state/queries";
import { StarIcon, AddDriver, Upload, Cloud, UserTick, UploadActive, ChevronDownIcon } from "@/icons";
import { queryClient } from "@/hooks/useGlobalContext";
import { toast } from "react-toastify";
import DeleteModal from "@/components/ui/modal/deletemodal";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { GrDocumentDownload } from "react-icons/gr";
import { IoMdClose } from "react-icons/io";
import { IoIosSearch } from "react-icons/io";
import { FaPlus } from "react-icons/fa6";
import { subYears } from "date-fns";
import Input from "@/components/form/input/InputField";
import Select from "@/components/form/Select";
import FilterComponent from "@/app/(admin)/(others-pages)/driver/components/Filter";
import { ApproveDocument } from "@/app/(admin)/(others-pages)/driver/components/ApproveDocument";
import { useRouter } from "next/navigation";


type driverDetails = {
    fullName: string;
    email: string;
    gender: string;
    dob: Date | null;
    nationalIdSsn: string;
    vehicleAvailability: string;
    model: string;
    plateNumber: string;
    vehicleType: string;
    address: string;
    [key: string]: string | Date | null;
}

const modalYearOptions = Array.from({ length: 2026 - 2000 }, (_, i) => ({
    value: `${2000 + i}`,
    label: `${2000 + i}`
}));
const vehicleOptions = [
    { value: "sedan", label: "Sedan" },
    { value: "van", label: "Van" },
    { value: "mini-van", label: "Mini Van" },
    { value: "suv", label: "SUV" },
]
type Filters = {
    name: string;
    shift: string;
    ratings: string;
    region: string;
    rides: string;
};


type SortConfig = {
    key: string;
    direction: 'asc' | 'desc';
};

// Inside your DriverTable component, add these state variables

export default function DriverTable() {
    const router = useRouter();
    const [open, setOpen] = useState(false);
    const [openApproveDocument, setOpenApproveDocument] = useState(false)
    const [currentStep, setCurrentStep] = useState(1);
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [selectedIds, setSelectedIds] = useState<number[]>([]);
    const [filterName, setFilterName] = useState<string>("")
    const [filters, setFilters] = useState<Filters>({
        name: "", shift: "", ratings: "", region: "", rides: ""
    })
    const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const steps = [
        { icon: AddDriver, active: AddDriver, label: "Driver Details", subtitle: "10 Questions" },
        { icon: Upload, active: UploadActive, label: "Upload Document", subtitle: "4 Documents" },
    ];
    const [driverType, setDriverType] = useState("company-driver")
    const { data: driverData, isLoading, refetch } = useQuery({
        queryKey: ["driverData"],
        queryFn: async () => {
            return getTableDriverDetails();
        },
        enabled: true,
    });

    useEffect(() => {
        refetch();
    }, [refetch]);

    const handleSelectOrder = (id: number) => {
        setSelectedIds(prev =>
            prev.includes(id)
                ? prev.filter(item => item !== id)
                : [...prev, id]
        );
    };

    const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.checked) {
            const allIds = sortedData.map((driver: any) => driver.id);
            setSelectedIds(allIds);
        } else {
            setSelectedIds([]);
        }
    };

    const [formData, setFormData] = useState<driverDetails>({
        fullName: "",
        email: "",
        gender: "",
        dob: null,
        nationalIdSsn: "",
        vehicleAvailability: "",
        model: "",
        plateNumber: "",
        vehicleType: "",
        address: "",
        countryCode: "+1",
        contactNo: "9876543210",
        shift: "morning",
        region: "North",
        rating: "4.5",
        city: "demo",
        state: "demo",
        zipCode: "demo",
        color: "Red",
        lastActive: "02-02-2025",
        driverStatus: "Active",
        insuranceExpiryDate: null,
        insuranceRenewalReminder: "true",
        vehicleRegistration: "true",
        vehicleDetails: "vehicleDetails",
        image: ""

    });
    const [driverLicenceFile, setDriverLicenceFile] = useState(null);
    const [insuranceFile, setInsuranceFile] = useState(null);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleDriverDob = (date: Date | null) => {
        setFormData((prev) => ({
            ...prev,
            dob: date,
        }));
    };
    const handleDriverInsurance = (date: Date | null) => {
        setFormData((prev) => ({
            ...prev,
            insuranceExpiryDate: date ? date.toISOString().split("T")[0] : "",
        }));
    };

    const handleSelectChange = (name: string, value: string) => {
        // If changing vehicle availability to "no", clear dependent fields
        if (name === "vehicleAvailability" && value === "no") {
            setFormData(prev => ({
                ...prev,
                [name]: value,
                model: "",
                plateNumber: "",
                vehicleType: "",
                insuranceExpiryDate: null
            }));
        } else {
            setFormData(prev => ({ ...prev, [name]: value }));
        }
    };

    const validate = () => {
        console.log("Validation started.");
        const newErrors: Record<string, string> = {};
        const pattern = /^[A-Z]-\d{3}-[A-Z]{2}$/;

        // Fields that are never required
        const neverRequired = [
            "city",
            "state",
            "zipCode",
            "color",
            "lastActive",
            "driverStatus",
            "image",
            "countryCode",
            "contactNo",
            "shift",
            "region",
            "rating",
            "insuranceRenewalReminder",
            "vehicleRegistration",
            "vehicleDetails"
        ];

        // Fields to skip if vehicleAvailability === 'no'
        const skipIfNoVehicle = [
            "model",
            "plateNumber",
            "vehicleType",
            "insuranceExpiryDate"
        ];

        const shouldSkipVehicleFields = formData.vehicleAvailability === "no";

        for (const key in formData) {
            // Skip fields that are never required
            if (neverRequired.includes(key)) continue;

            // Skip vehicle-related fields if vehicle availability is 'no'
            if (shouldSkipVehicleFields && skipIfNoVehicle.includes(key)) continue;

            // Validate all other required fields
            if (!formData[key]) {
                newErrors[key] = "This field is required";
            }

            if (key == "plateNumber" && !pattern.test(formData[key].toString())) {
                newErrors[key] = "Please enter a valid code in the format: A-001-BB";
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };


    const [deleteProfile, setDeleteProfile] = useState(false);
    const [filteredData, setFiltereData] = useState([]);
    const [selectedId, setSelectedId] = useState<string | null>(null);

    const validateFiles = () => {
        console.log("File validation started.");

        const newErrors: Record<string, string> = {};

        if (!driverLicenceFile) {
            newErrors.driverLicenceFile = "Driver Licence file is required";
            console.warn("Validation error: Driver Licence file is missing.");
        }

        if (!insuranceFile) {
            newErrors.insuranceFile = "Insurance Copy file is required";
            console.warn("Validation error: Insurance file is missing.");
        }

        setErrors(newErrors);

        const isValid = Object.keys(newErrors).length === 0;
        console.log("File validation completed. Valid:", isValid);

        if (!isValid) {
            console.table(newErrors); // Neat table view of errors in the console
        }

        return isValid;
    };



    const handleSubmit = () => {
        console.log("Form submission started.");
        setIsSubmitting(true); // Start loading

        const isValid = validate();
        console.log("Validation result:", isValid);

        if (!isValid) {
            console.warn("Validation failed. Submission aborted.");
            setIsSubmitting(false); // Stop loading if validation fails
            return;
        }

        // Simulate API call delay (remove this in production)
        setTimeout(() => {
            console.log("Validation passed. Proceeding to step 2.");
            setCurrentStep(2);
            setIsSubmitting(false); // Stop loading after successful validation
        }, 1000);
    };

    const addDriverMutation = useMutation({
        mutationFn: (data: any) => {

            return addTableDriverDetails(data);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["driverData"] });
            toast.success("Driver Added Successfully", { autoClose: 5000, position: "top-center", })
            setOpen(false)
            setCurrentStep(1)
            setErrors({});
            setFiltereData([]);
            setDriverLicenceFile(null);
            setInsuranceFile(null);

            setFormData({
                fullName: "",
                email: "",
                gender: "",
                dob: null,
                nationalIdSsn: "",
                vehicleAvailability: "",
                model: "",
                plateNumber: "",
                vehicleType: "",
                address: "",
                countryCode: "+1",
                contactNo: "9876543210",
                shift: "morning",
                region: "North",
                rating: "4.5",
                city: "demo",
                state: "demo",
                zipCode: "demo",
                color: "Red",
                lastActive: "02-02-2025",
                driverStatus: "Active",
                insuranceExpiryDate: null,
                insuranceRenewalReminder: "true",
                vehicleRegistration: "true",
                vehicleDetails: "vehicleDetails",
                image: ""
            })
        },
        onError: (err) => {
            console.error(err)
        },
    });

    // Update your handleFormSubmit function
    const handleFormSubmit = () => {
        if (validateFiles()) {
            setIsSubmitting(true); // Start loading

            const payLoad = {
                ...formData,
                driverLicenceFile: driverLicenceFile,
                insuranceFile: insuranceFile,
                driverType: driverType
            };

            addDriverMutation.mutate(payLoad, {
                onSettled: () => {
                    setIsSubmitting(false); // Stop loading when mutation completes (success or error)
                }
            });
        }
    };

    const downLoadDriverMutation = useMutation({
        mutationFn: (id: string) => downloadReport(id),
        onSuccess: async (res) => {
            const url = window.URL.createObjectURL(new Blob([res], { type: 'text/csv' }));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'data.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        },
        onError: (err) => {
            console.error("Download error", err);
        },
    });
    const deleteDriverMutation = useMutation({
        mutationFn: (data: string) => {
            return deleteDriver(data);
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["driverData"] });
            setDeleteProfile(false)
            toast.success("Driver Deleted Successfully", { autoClose: 5000, position: "top-center", })

        },
        onError: (err) => {
            console.error(err)
        },
    });
    const disableDriverMutation = useMutation({
        mutationFn: async (data: any) => {
            return disableDriverQuery(data);
        },
        onSuccess: (data: { driverStatus: string }) => {
            queryClient.invalidateQueries({ queryKey: ["driverData"] });
            toast.success(`Driver ${data?.driverStatus} Successfully`, { autoClose: 5000, position: "top-center", })
        },
        onError: (err) => {
            console.error(err)
        },
    });

    const handleDeleteDriver = (driverId: string) => {

        console.log("Delete Driver ID:", driverId);


        if (driverId) {
            deleteDriverMutation.mutate(driverId);
        }
    };
    const handleDisableDriver = (driverId: string, status: string) => {
        const data = { driverId, status }
        if (data) {
            disableDriverMutation.mutate(data);
        }
    };

    function filtersDataFuc() {
        if (!driverData || !Array.isArray(driverData)) {
            console.warn('Driver data is not an array:', driverData);
            setFiltereData([]);
            return;
        }
        const data = driverData.filter((driver: any) => {
            const driverName = driver.fullName?.toLowerCase() || "";
            const matchesSearch = !searchTerm.trim() || driverName.includes(searchTerm.toLowerCase());
            const matchesDriverSearch = !filterName.trim() || driverName.includes(filterName.toLowerCase());
            const matchesName = !filters.name || driverName.includes(filters.name.toLowerCase());
            const matchesShifts = !filters.shift || (driver.shift?.toLowerCase() === filters.shift.toLowerCase());
            const matchesRatings = !filters.ratings || (String(driver.rating) === String(filters.ratings));
            const matchesRegion = !filters.region || (driver.region?.toLowerCase() === filters.region.toLowerCase());
            const matchesRides = !filters.rides || (String(driver.rides) === String(filters.rides));
            return (
                matchesSearch &&
                matchesName &&
                matchesShifts &&
                matchesRatings &&
                matchesRegion &&
                matchesRides && matchesDriverSearch
            );
        });
        setFiltereData(data);
    }

    useEffect(() => {
        filtersDataFuc();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getDriverStatusClass = (status) => {
        if (status === "Active") return "text-[#13BB76]";
        if (status === "Inactive") return "text-[#8F8CD6]";
        if (status === "Suspended") return "text-[#FF4032]";
        if (status === "Enroute") return "text-[#1E90FF]";
        return "text-[#FF8C00]";
    };



    // Add this sorting function
    const requestSort = (key: string) => {
        let direction: 'asc' | 'desc' = 'asc';
        if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
            direction = 'desc';
        }
        setSortConfig({ key, direction });
    };

    // Add this function to get the sort icon
    const getSortIcon = (key: string) => {
        if (!sortConfig || sortConfig.key !== key) return '⇅';
        return sortConfig.direction === 'asc' ? '↑' : '↓';
    };


    // Update your filteredData sorting logic
    const sortedData = React.useMemo(() => {
        if (!sortConfig) return filteredData;

        return [...filteredData].sort((a, b) => {
            // Handle numeric fields differently
            if (['id', 'rating', 'rides', 'earnings'].includes(sortConfig.key)) {
                const aValue = parseFloat(a[sortConfig.key]) || 0;
                const bValue = parseFloat(b[sortConfig.key]) || 0;
                return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
            }

            // Handle date fields differently if needed
            if (sortConfig.key === 'dateTime') {
                const aDate = new Date(a[sortConfig.key]);
                const bDate = new Date(b[sortConfig.key]);
                return sortConfig.direction === 'asc'
                    ? aDate.getTime() - bDate.getTime()
                    : bDate.getTime() - aDate.getTime();
            }

            // Default string comparison
            const aValue = a[sortConfig.key]?.toString().toLowerCase() || '';
            const bValue = b[sortConfig.key]?.toString().toLowerCase() || '';

            if (aValue < bValue) {
                return sortConfig.direction === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return sortConfig.direction === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }, [filteredData, sortConfig]);


    return (
        <>
            <div className="tabled">
                <div className="flex justify-end">
                    <button
                        onClick={() => setOpen(true)}
                        type="button"
                        className="flex items-center gap-2 text-white bg-cstm-blue-700  hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 font-medium rounded-full text-sm px-5 py-2.5 text-center me-2 mb-4 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                    >
                        <FaPlus />
                        Add New Driver
                    </button>
                </div>
                <div className="overflow-visible rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
                    <div className="header-bar flex items-center justify-between py-1 px-3 bg-table-head rounded-t-[12px]">
                        {/* Search Bar */}
                        <form className="flex-1 max-w-md">
                            <label
                                className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
                            <div className="relative">
                                <div
                                    className="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                                    <IoIosSearch className="w-[20px] h-[20px] text-[#050013]" />
                                </div>
                                <input
                                    type="search"
                                    id="default-search"
                                    className="block w-3/4 p-2 ps-10 text-sm text-gray-900 border border-transparent rounded-full focus:ring-blue-500 focus:border-blue-500 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500 bg-white shadow-[0_10px_40px_0_#0000000D]"
                                    placeholder="Search here"
                                    onChange={(e) => setSearchTerm(e.target.value)}

                                />
                            </div>
                        </form>
                        {/* Buttons Container */}
                        <div className="flex items-center gap-2">

                            <FilterComponent filteredData={driverData} setFilters={setFilters} filtersDataFuc={filtersDataFuc} />
                            <button
                                type="button"
                                aria-label="clock"
                                onClick={() => {
                                    setFilters({ name: "", shift: "", ratings: "", region: "", rides: "" })
                                    setFilterName("")
                                }}
                                className="flex items-center justify-center p-1 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                            >
                                <PiClockCounterClockwiseLight size={24} />
                            </button>
                            <button
                                aria-label="download"
                                onClick={() => downLoadDriverMutation.mutate(selectedIds[0].toString())}
                                type="button"
                                disabled={selectedIds.length !== 1}
                                className="flex items-center justify-center p-2 text-[#76787A] focus:outline-none bg-white rounded-full border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                            >
                                {/* <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                    className="size-4">
                                    <path fillRule="evenodd"
                                        d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z"
                                        clip-rule="evenodd" />
                                </svg> */}
                            </button>
                        </div>
                    </div>
                    <div className="max-w-full overflow-x-auto custom-scrollbar">
                        {/* <div className="max-w-[1082px] min-w-[-webkit-fill-available]"> */}
                        <div className="max-w-[992px] min-w-[-webkit-fill-available]">
                            <Table>
                                {/* Table Header */}
                                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05] bg-white text-[#76787A]">
                                    <TableRow>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-[#76787A] text-start text-theme-xs dark:text-gray-400 text-[12px]"
                                        >
                                            <label className="checkbox">
                                                <input type="checkbox"
                                                    placeholder="Select Order"
                                                    name="selectOrder"
                                                    checked={selectedIds.length === sortedData.length && sortedData.length > 0}
                                                    onChange={handleSelectAll}
                                                    className="form-checkbox text-blue-500"
                                                />
                                                <span></span>
                                            </label>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('id')}
                                                className="flex items-center"
                                            >
                                                ID
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('id')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('fullName')}
                                                className="flex items-center"
                                            >
                                                Name
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('fullName')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('contactNo')}
                                                className="flex items-center"
                                            >
                                                Contact No
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('contactNo')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('driverStatus')}
                                                className="flex items-center"
                                            >
                                                Status
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('driverStatus')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('shift')}
                                                className="flex items-center"
                                            >
                                                Shift
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('shift')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('rating')}
                                                className="flex items-center"
                                            >
                                                Ratings
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('rating')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('region')}
                                                className="flex items-center"
                                            >
                                                Region
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('region')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('plateNumber')}
                                                className="flex items-center"
                                            >
                                                Vehicle Details
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('plateNumber')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('rides')}
                                                className="flex items-center"
                                            >
                                                Rides
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('rides')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('earnings')}
                                                className="flex items-center"
                                            >
                                                Earnings
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('earnings')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="px-4 py-3 font-medium text-76787A text-start text-theme-xs dark:text-gray-400 text-[12px] whitespace-nowrap"
                                        >
                                            <button
                                                onClick={() => requestSort('registrationstatus')}
                                                className="flex items-center"
                                            >
                                                Verification
                                                <span className="px-1 text-[11px]">
                                                    {getSortIcon('registrationstatus')}
                                                </span>
                                            </button>
                                        </TableCell>
                                        <TableCell
                                            isHeader
                                            className="py-3 font-medium text-76787A text-right text-theme-xs dark:text-gray-400 text-[12px]"
                                        >
                                            &nbsp;
                                        </TableCell>
                                    </TableRow>
                                </TableHeader>
                                {/* Table Body */}
                                <TableBody className="divide-y dark:divide-white/[0.05] bg-white">
                                    {isLoading ? (
                                        <TableRow>
                                            <TableCell className="text-center py-8">
                                                Loading drivers...
                                            </TableCell>
                                        </TableRow>
                                    ) : !Array.isArray(sortedData) || sortedData.length === 0 ? (
                                        <TableRow>
                                            <TableCell className="text-center py-8">
                                                No drivers found
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        sortedData.map((order: any, index: number) => (
                                            <TableRow
                                                key={index}
                                                className="cursor-pointer dark:hover:bg-gray-800"
                                            >

                                                <TableCell className="px-4 py-2">
                                                    <label className="checkbox">
                                                        <input
                                                            type="checkbox"
                                                            checked={selectedIds.includes(order.id)}
                                                            onChange={() => handleSelectOrder(order.id)}
                                                            className="form-checkbox text-blue-500"
                                                        />
                                                        <span></span>
                                                    </label>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3 ">
                                                    <p className="text-[#050013] text-xs">#{order?.id}</p>
                                                </TableCell>


                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className="text-[#050013] text-[12px] capitalize"
                                                        onClick={() => router.push(`/driver/${order?.id}`)}>{order?.fullName}</p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className="text-[#050013] text-[12px]">{order?.contactNo}</p>
                                                </TableCell>

                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className={`text-[13px] capitalize ${getDriverStatusClass(order?.driverStatus)}`}>
                                                        {order?.driverStatus}
                                                    </p>
                                                    {/* <p className="text-[#050013] text-[12px]">{order?.driverStatus}</p> */}
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className="text-[#050013] text-[12px] capitalize">{order?.shift}</p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3 ">
                                                    <p className="text-[#050013] text-[12px] flex items-center gap-1">{order?.rating} <StarIcon /></p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className="text-[#050013] text-[12px] capitalize">{order?.region}</p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className="text-[#050013] text-[12px] uppercase">{order?.plateNumber}</p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className="text-[#050013] text-[12px]">{order?.rides}</p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className="text-[#050013] text-[12px]">€{order?.earnings} </p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <p className={`${order?.registrationstatus === "Verified" ? "text-[#00CA77]" : "text-[#FF7C00]"} flex  text-[11px] gap-1 items-center`}>
                                                        <span className={`block h-[6px] w-[6px] rounded-full ${order?.registrationstatus === "Verified" ? "bg-[#00CA77]" : "bg-[#FF7C00]"}`}></span>
                                                        {order?.registrationstatus ? "Verified" : "Pending"}</p>
                                                </TableCell>
                                                <TableCell
                                                    className="px-4 py-3">
                                                    <Tippy
                                                        trigger="click"
                                                        content={
                                                            <div className="bg-white text-gray-900">
                                                                <div className="flex flex-col space-y-1 p-1">
                                                                    <button
                                                                        className=" text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start"
                                                                        onClick={() => router.push(`/driver/${order?.id}?edit=true`)}>
                                                                        Edit Profile
                                                                    </button>
                                                                    <button
                                                                        onClick={() => handleDisableDriver(order?.id, "Suspended")}
                                                                        className=" text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                        Suspend Driver
                                                                    </button>
                                                                    <button
                                                                        onClick={() => handleDisableDriver(order?.id, "Active")}
                                                                        className=" text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                        Reinstate Driver
                                                                    </button>
                                                                    <button
                                                                        onClick={() => handleDisableDriver(order?.id, "Inactive")}
                                                                        className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                        Deactivate Driver
                                                                    </button>
                                                                    <button
                                                                        className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start"
                                                                        onClick={() => setOpenApproveDocument(true)} >    Approve Document
                                                                    </button>
                                                                    <button
                                                                        className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start"
                                                                        onClick={
                                                                            () => toast.success("Notification sent!", { autoClose: 5000, position: "top-center", })
                                                                        }>
                                                                        Send Notification
                                                                    </button>
                                                                    <button

                                                                        onClick={() => { downLoadDriverMutation.mutate(order?.id) }}
                                                                        className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">

                                                                        Download Report
                                                                    </button>
                                                                    <button
                                                                        className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                        Trip Management
                                                                    </button>

                                                                    <button
                                                                        onClick={() => {
                                                                            setSelectedId(order.id);
                                                                            setDeleteProfile(true);
                                                                            hideAll();
                                                                        }}
                                                                        className="text-[#76787A] hover:text-[#050013] px-2 py-1 rounded w-full text-start">
                                                                        Delete Profile
                                                                    </button>

                                                                    {selectedId && (
                                                                        <DeleteModal
                                                                            id={selectedId}
                                                                            isOpen={deleteProfile}
                                                                            setIsOpen={setDeleteProfile}
                                                                            handleDeleteDriver={(id) => {
                                                                                handleDeleteDriver(id);
                                                                                setSelectedId(null);
                                                                            }}
                                                                            title="Delete Document"
                                                                            message="Are you sure you want to delete this document? This cannot be undone."
                                                                        />
                                                                    )}
                                                                </div>
                                                            </div>
                                                        }
                                                        interactive={true}
                                                        placement="right"
                                                        theme="light"
                                                        arrow={false}
                                                        duration={0}
                                                        className="!bg-white !text-gray-900 border border-gray-200 rounded-lg shadow-sm"
                                                    >
                                                        <button type="button" className="focus:outline-none text-[#76787A]"
                                                            aria-label="evenodd"
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                                fill="currentColor" className="size-6">
                                                                <path
                                                                    fillRule="evenodd"
                                                                    d="M10.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
                                                                    clipRule="evenodd"
                                                                />
                                                            </svg>
                                                        </button>
                                                    </Tippy>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                    <Dialog open={open} onClose={setOpen} className="relative z-100000"
                        style={{ borderTopLeftRadius: '50px' }}>
                        <DialogBackdrop
                            transition
                            className="fixed inset-0 bg-[#2a2a2a] opacity-60 transition-opacity duration-500 ease-in-out data-closed:opacity-0"
                        />
                        <div className="fixed inset-0 overflow-hidden">
                            <div className="absolute inset-0 overflow-hidden">
                                <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                                    <DialogPanel
                                        transition
                                        style={{ borderTopLeftRadius: '50px !important' }}
                                        className="w-full sm:w-[500px] md:w-[700px] lg:w-[500px] xl:w-[600px] pointer-events-auto relative max-w-4xl transform transition duration-500 ease-in-out data-closed:translate-x-full sm:duration-700 rounded-tl-[50px]"
                                    >
                                        <div
                                            className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl rounded-2xl">
                                            <div className="sm:px-6 py-8 bg-[#F6F8FB]">
                                                <DialogTitle
                                                    className="text-[20px] font-semibold text-gray-900 flex items-center justify-between py-5 align-center"><div>Driver Details</div> <IoMdClose className="text-[#76787A] hover:text-[#3324E3]" size={20} onClick={() => setOpen(false)} />
                                                </DialogTitle>
                                                <ul className="flex items-center w-full">
                                                    {steps.map((step, index) => {
                                                        const stepIndex = index + 1;
                                                        const isActive = currentStep === stepIndex;
                                                        const isSecondStep = currentStep == 2;
                                                        const IconComponent = isSecondStep ? step.active : step.icon; // Get the icon component dynamically

                                                        return (
                                                            <li key={stepIndex}
                                                                className="flex items-center w-full">
                                                                {/* Step Icon Container */}
                                                                <div className="relative flex items-center">
                                                                    <div
                                                                        className={`size-12 flex justify-center items-center rounded-full border ${isSecondStep && stepIndex == 1 ? 'border-green' : 'border-gray-300'}`}>
                                                                        <IconComponent size={24}
                                                                            className='text-gray-600' />
                                                                    </div>
                                                                    {stepIndex == 1 && isSecondStep &&
                                                                        <div className="absolute bottom-[20px] left-[25px]"><UserTick /></div>}
                                                                </div>

                                                                {/* Step Text */}
                                                                <div className="flex flex-col ml-2">
                                                                    <span className={`text-sm ${isActive ? (isSecondStep && stepIndex == 2) ? 'text-blue-6 font-medium' : 'text-[#050013] font-medium' : 'text-gray-900'}`}>
                                                                        {step.label}
                                                                    </span>
                                                                    <span
                                                                        className="text-xs text-gray-500">{step.subtitle}</span>
                                                                </div>

                                                                {/* Step Connector */}
                                                                {stepIndex < steps.length && (
                                                                    <hr className={`flex-1 mx-4 border-t h-0 transition-colors duration-300 ${isSecondStep && stepIndex === 1
                                                                        ? 'border-blue-600'
                                                                        : 'border-gray-300'
                                                                        }`} />
                                                                )}
                                                            </li>
                                                        );
                                                    })}
                                                </ul>
                                            </div>
                                            <div className="relative flex-1 px-4 sm:px-6 overflow-y-scroll custom-scrollbar mb-6">
                                                <div>
                                                    {/* Stepper Content */}
                                                    {currentStep === 1 && (
                                                        <div className="mt-5 sm:mt-8">
                                                            <div
                                                                className="flex justify-center items-center  border-gray-200 rounded-xl">
                                                                <div className="w-full">
                                                                    <p
                                                                        className="text-sm text-[#050013] font-medium dark:text-white mb-3">
                                                                        Profile Overiew
                                                                    </p>

                                                                    <RadioGroup value={driverType} onChange={setDriverType} aria-label="Server size" className="mb-3"  >
                                                                        <Field className="flex items-center gap-4 py-2">
                                                                            <Radio
                                                                                value="company-driver"
                                                                                id="company-driver"
                                                                                className={`group flex size-5 items-center justify-center rounded-full border bg-white data-checked:bg-blue-400 ${driverType == "company-driver" ? "cstm-radio" : "radio-border"}`}
                                                                            >
                                                                                <span className="invisible size-2 rounded-full bg-white group-data-checked:visible" />
                                                                            </Radio>
                                                                            <Label htmlFor="company-driver" className={`text-[13px] ${driverType == "company-driver" ? "font-medium text-[#050013]" : "font-normal text-[#76787A]"}`}>Company Driver</Label>
                                                                            <Radio
                                                                                value="contract-driver"
                                                                                id="contract-driver"
                                                                                className={`group flex size-5 items-center justify-center rounded-full border bg-white data-checked:bg-blue-400 ${driverType == "contract-driver" ? "cstm-radio" : "radio-border"}`}
                                                                            >
                                                                                <span className="invisible size-2 rounded-full bg-white group-data-checked:visible" />
                                                                            </Radio>
                                                                            <Label htmlFor="contract-driver" className={`text-[13px] ${driverType == "contract-driver" ? "font-medium text-[#050013]" : "font-normal text-[#76787A]"}`}>Contract Driver</Label>
                                                                        </Field>

                                                                    </RadioGroup>

                                                                    <div className="pb-2">
                                                                        <Input type="text"
                                                                            placeholder="Full Name*"
                                                                            onChange={handleChange}
                                                                            name="fullName"
                                                                        />
                                                                        {errors?.fullName && <p className="text-red-500 text-sm">{errors?.fullName}</p>}
                                                                    </div>


                                                                    <div className="py-2">
                                                                        <Input type="email"
                                                                            placeholder="Email ID*"
                                                                            onChange={(e) => {
                                                                                const email = e.target.value
                                                                                const isValidEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)

                                                                                if (isValidEmail) {
                                                                                    setFormData((prev) => ({ ...prev, email }))
                                                                                    setErrors((prev) => ({ ...prev, email: '' }))
                                                                                } else {
                                                                                    setErrors((prev) => ({ ...prev, email: 'Invalid email address' }))
                                                                                }
                                                                            }}
                                                                            name="email"
                                                                        />
                                                                        {errors?.email && <p className="text-red-500 text-sm">{errors?.email}</p>}
                                                                    </div>

                                                                    <div className="flex gap-4 w-full py-2">
                                                                        <div className="relative w-full">
                                                                            <Select
                                                                                options={[{ value: "male", label: "Male" },
                                                                                { value: "other", label: "Other" },
                                                                                { value: "female", label: "Female" }]}
                                                                                placeholder="Gender*"
                                                                                onChange={(e) => handleSelectChange("gender", e)}
                                                                                className="dark:bg-dark-900"
                                                                            />

                                                                            <span
                                                                                className="absolute text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[22px] dark:text-gray-400">
                                                                                <ChevronDownIcon />
                                                                            </span>
                                                                            {errors?.gender && <p className="text-red-500 text-sm">{errors?.gender}</p>}
                                                                        </div>


                                                                        <div className="relative w-full">
                                                                            <DatePicker
                                                                                selected={formData?.dob}
                                                                                className="w-full p-2 border border-gray-300 rounded-lg text-gray-800"
                                                                                onChange={(date: Date | null) =>
                                                                                    handleDriverDob(date)
                                                                                }
                                                                                showYearDropdown
                                                                                showMonthDropdown
                                                                                scrollableYearDropdown
                                                                                dateFormat="dd/MM/yyyy"
                                                                                placeholderText="DOB"
                                                                                yearDropdownItemNumber={100}
                                                                                maxDate={subYears(new Date(), 18)} // 🚫 Disable future dates that make user < 18
                                                                            />
                                                                            <span
                                                                                className="absolute text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[22px] dark:text-gray-400">
                                                                                <ChevronDownIcon />
                                                                            </span>
                                                                            {errors?.dob && <p className="text-red-500 text-sm">{errors?.dob}</p>}

                                                                        </div>

                                                                    </div>

                                                                    <Input type="text"
                                                                        placeholder="National ID/SSN"
                                                                        className="mt-3"
                                                                        onChange={handleChange}
                                                                        name="nationalIdSsn"
                                                                    />
                                                                    {errors?.nationalIdSsn && <p className="text-red-500 text-sm">{errors?.nationalIdSsn}</p>}

                                                                    <p
                                                                        className="text-sm text-[#050013] font-medium dark:text-white mb-3 pt-5">
                                                                        Vehicle Details
                                                                    </p>

                                                                    <div className="mb-3 mt-3 ">
                                                                        <div className="relative">
                                                                            <Select
                                                                                options={[{ value: "yes", label: "Yes" },
                                                                                { value: "no", label: "No" }]}
                                                                                placeholder="Vehicle Availability"
                                                                                onChange={(e) => handleSelectChange("vehicleAvailability", e)}
                                                                                className="dark:bg-dark-900"
                                                                            />

                                                                            <span
                                                                                className="absolute text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[22px] dark:text-gray-400">
                                                                                <ChevronDownIcon />
                                                                            </span>
                                                                        </div>
                                                                        {errors?.vehicleAvailability && <p className="text-red-500 text-sm">{errors?.vehicleAvailability}</p>}
                                                                    </div>


                                                                    {formData.vehicleAvailability === "yes" && (
                                                                        <>
                                                                            <div className="grid grid-cols-2 gap-2 mb-3">
                                                                                <div>
                                                                                    <div className="relative">
                                                                                        <Select
                                                                                            options={modalYearOptions}
                                                                                            placeholder="Model"
                                                                                            onChange={(e) => handleSelectChange("model", e)}
                                                                                            className="dark:bg-dark-900 w-100"
                                                                                        />
                                                                                        <span
                                                                                            className="absolute text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[22px] dark:text-gray-400">
                                                                                            <ChevronDownIcon />
                                                                                        </span>
                                                                                    </div>
                                                                                    {errors?.model && <p className="text-red-500 text-sm">{errors?.model}</p>}

                                                                                </div>
                                                                                <div className="relative">
                                                                                    <div>
                                                                                        <Input type="text"
                                                                                            placeholder="Plate Number"
                                                                                            className="mb-3"
                                                                                            onChange={handleChange}
                                                                                            name="plateNumber"
                                                                                        />
                                                                                        {errors?.plateNumber && <p className="text-red-500 text-sm">{errors?.plateNumber}</p>}
                                                                                        <span
                                                                                            className="absolute text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[22px] dark:text-gray-400">
                                                                                            <ChevronDownIcon />
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <div className="grid grid-cols-2 gap-2 mb-5">
                                                                                <div>
                                                                                    <div className="relative">
                                                                                        <Select
                                                                                            options={vehicleOptions}
                                                                                            placeholder="Vehicle Type"
                                                                                            onChange={(e) => handleSelectChange("vehicleType", e)}
                                                                                            className="dark:bg-dark-900 w-100"
                                                                                        />
                                                                                        <span
                                                                                            className="absolute text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[22px] dark:text-gray-400">
                                                                                            <ChevronDownIcon />
                                                                                        </span>
                                                                                    </div>
                                                                                    {errors?.vehicleType && <p className="text-red-500 text-sm">{errors?.vehicleType}</p>}

                                                                                </div>

                                                                                <div>
                                                                                    <div className="relative w-full">
                                                                                        <DatePicker
                                                                                            selected={formData.insuranceExpiryDate ? new Date(formData.insuranceExpiryDate) : null}
                                                                                            className="w-full p-2 border border-gray-300 rounded-lg text-gray-800"
                                                                                            onChange={(date: Date | null) =>
                                                                                                handleDriverInsurance(date)
                                                                                            }
                                                                                            showYearDropdown
                                                                                            showMonthDropdown
                                                                                            scrollableYearDropdown
                                                                                            dateFormat="dd/MM/yyyy"
                                                                                            placeholderText="Insurance Expiry Date"
                                                                                            yearDropdownItemNumber={100} // Number of years to show in dropdown
                                                                                            minDate={new Date()}
                                                                                        />
                                                                                        <span
                                                                                            className="absolute text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[22px] dark:text-gray-400">
                                                                                            <ChevronDownIcon />
                                                                                        </span>
                                                                                    </div>
                                                                                    {/* <div className="relative">
                                                                                <Select
                                                                                    options={modalYearOptions}
                                                                                    placeholder="Insurance Expiry Date"
                                                                                    onChange={(e) => handleSelectChange("model", e)}
                                                                                    className="dark:bg-dark-900 w-100"
                                                                                />
                                                                                <span
                                                                                    className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                                                                                    <ChevronDownIcon />
                                                                                </span>
                                                                            </div> */}
                                                                                    {errors?.insuranceExpiryDate && <p className="text-red-500 text-sm">{errors?.insuranceExpiryDate}</p>}

                                                                                </div>
                                                                            </div>
                                                                        </>
                                                                    )}
                                                                    <div className="relative mb-[40px]">
                                                                        <p
                                                                            className="text-sm text-[#050013] font-medium dark:text-white mb-3">
                                                                            Address Details
                                                                        </p>

                                                                        <Input type="text"
                                                                            placeholder="Current Address"
                                                                            className=""
                                                                            onChange={handleChange}
                                                                            name="address"
                                                                        />
                                                                        {errors?.address && <p className="text-red-500 text-sm">{errors?.address}</p>}
                                                                        <span
                                                                            className="absolute mt-[10px] text-[#050013] -translate-y-1/2 pointer-events-none right-3 top-[45px] dark:text-gray-400">
                                                                            <ChevronDownIcon />
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </div>

                                                    )}

                                                    {currentStep === 2 && (
                                                        <div className="mt-5 sm:mt-8">
                                                            <p
                                                                className="text-sm font-medium text-[#050013] pb-3 dark:text-white">
                                                                Driver Licence<span className="text-red-500">*</span>
                                                            </p>
                                                            <div
                                                                className="shadow-none p-4 mt-3 bg-white border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
                                                                <div
                                                                    className="flex items-center justify-center w-full">
                                                                    <label
                                                                        className="flex flex-col items-center justify-center w-full  border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-8">
                                                                        <div
                                                                            className="flex items-center justify-center pt-5 pb-6">
                                                                            <Cloud />
                                                                           
                                                                            <p className="text-sm text-dark-grey dark:text-gray-400">
                                                                                <span className="px-3 text-[14px]">Click or drag file to this area to upload</span>
                                                                            </p>
                                                                        </div>
                                                                        <input id="dropzone-file" type="file"
                                                                            name="driverLicenceFile"
                                                                            onChange={(e) => { setDriverLicenceFile(e.target.files[0]) }}
                                                                            className="hidden" />
                                                                    </label>
                                                                </div>
                                                                {driverLicenceFile && (
                                                                    <p className="text-[12px] py-2 text-green-500">{driverLicenceFile.name}</p>
                                                                )}
                                                                {errors.driverLicenceFile && (
                                                                    <p className="text-[12px] py-2 text-red-500">{errors.driverLicenceFile}</p>
                                                                )}
                                                            </div>
                                                            <p className="text-[#76787A] text-[13px] py-3">Formats accepted are PNG & JPG</p>
                                                            <p className="text-[#050013] text-[13px] mt-2">If you do not have a file you can see the sample below</p>
                                                            <div
                                                                className="bg-tables p-3 flex justify-between items-center w-full mt-3"
                                                                style={{ borderRadius: '10px' }}>
                                                                <div>
                                                                    <p className="text-dark text-sm font-semibold mb-1">Sample Certificate</p>
                                                                    <p className="text-gray-400 text-sm">PNG
                                                                        1.2MB</p>
                                                                </div>
                                                                <div
                                                                    className="flex items-center gap-2 text-blue-800 font-semibold"> {/* Group icon & text */}
                                                                    <GrDocumentDownload className="w-[20px] h-[20px]" />
                                                                    <p className="text-sm">Download</p>

                                                                </div>
                                                            </div>
                                                            <div
                                                                className="flex justify-end items-center gap-2 py-6 border-b">
                                                                <button
                                                                    type="button"
                                                                    aria-label="Download"

                                                                    className="py-2.5 px-10 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                                                    style={{ borderRadius: '50px' }}
                                                                >
                                                                    Cancel
                                                                </button>

                                                                <button
                                                                    type="button"
                                                                    aria-label="Download"

                                                                    className="text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 flex items-center gap-2 dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700"
                                                                    style={{ borderRadius: '50px' }}
                                                                >
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        fill="none"
                                                                        viewBox="0 0 24 24"
                                                                        strokeWidth="1.5"
                                                                        stroke="currentColor"
                                                                        className="size-4"
                                                                    >
                                                                        <path
                                                                            strokeLinecap="round"
                                                                            strokeLinejoin="round"
                                                                            d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
                                                                        />
                                                                    </svg>
                                                                    <span>Upload</span>
                                                                </button>
                                                            </div>
                                                            <p
                                                                className="text-sm font-medium text-[#050013] dark:text-white pt-6">
                                                                Insurance Copy<span className="text-red-500">*</span>
                                                            </p>
                                                            <div
                                                                className="max p-4 mt-3 bg-white border border-gray-200 rounded-lg shadow-none dark:bg-gray-800 dark:border-gray-700">
                                                                <div
                                                                    className="flex items-center justify-center w-full">
                                                                    <label
                                                                        className="flex flex-col items-center justify-center w-full  border-2 border-gray-300 border-dashed rounded-lg cursor-pointer py-5">
                                                                        <div
                                                                            className="flex items-center justify-center pt-5 pb-6">
                                                                            <Cloud />
                                                                            {/* <svg
                                                                                className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400"
                                                                                aria-hidden="true"
                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                fill="none" viewBox="0 0 20 16">
                                                                                <path stroke="currentColor"
                                                                                    stroke-linecap="round"
                                                                                    stroke-linejoin="round"
                                                                                    stroke-width="2"
                                                                                    d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                                                                            </svg> */}
                                                                            <p className="flex items-center justify-center pt-5 pb-6 text-[14px]">
                                                                                <span className="px-3 text-dark-grey dark:text-gray-400">Click or drag file to this area to upload</span>
                                                                            </p>



                                                                        </div>
                                                                        <input id="dropzone-file" type="file"
                                                                            name="insuranceFile"
                                                                            className="hidden"
                                                                            onChange={(e) => setInsuranceFile(e.target.files[0])}
                                                                        />
                                                                    </label>
                                                                </div>
                                                                {insuranceFile && (
                                                                    <p className="text-[12px] py-2 text-green-500">{insuranceFile?.name}</p>
                                                                )}
                                                                {errors.insuranceFile && (
                                                                    <p className="text-[12px] py-2 text-red-500">{errors.insuranceFile}</p>
                                                                )}
                                                            </div>
                                                            <p className="text-[#76787A] text-[13px] py-3">Formats accepted are PNG & JPG</p>
                                                            <p className="text-050013 mt-2">If you do not have a file you can see the sample below</p>
                                                            <div
                                                                className="bg-tables p-3 flex justify-between items-center w-full mt-3"
                                                                style={{ borderRadius: '10px' }}>
                                                                <div>
                                                                    <p className="text-dark text-sm font-semibold mb-1">Sample Certificate</p>
                                                                    <p className="text-gray-400 text-sm">PNG
                                                                        1.2MB</p>
                                                                </div>
                                                                <div
                                                                    className="flex items-center gap-2 text-blue-800 font-semibold"> {/* Group icon & text */}
                                                                    <GrDocumentDownload className="w-[20px] h-[20px]" />
                                                                    <p className="text-sm text-blue-800">Download</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )}

                                                    {/* Button Group */}


                                                </div>

                                            </div>

                                            <div className="w-full flex justify-end border-t border-grey-700 py-6">

                                                {currentStep === 1 ? (
                                                    <button
                                                        onClick={handleSubmit}
                                                        type="button"
                                                        disabled={isSubmitting}
                                                        className={`bg-cstm-blue-700 flex items-center gap-2 text-white bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 font-medium rounded-full text-sm px-6 py-3 text-center me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 ${isSubmitting ? 'opacity-75 cursor-not-allowed' : ''
                                                            }`}
                                                    >
                                                        {isSubmitting ? (
                                                            <>
                                                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                                </svg>
                                                                Processing...
                                                            </>
                                                        ) : (
                                                            'Continue'
                                                        )}
                                                    </button>
                                                ) : null}

                                                {currentStep === 2 ? (
                                                    <button
                                                        type="button"
                                                        onClick={handleFormSubmit}
                                                        disabled={isSubmitting || addDriverMutation.isPending}
                                                        className={`bg-cstm-blue-700 flex items-center gap-2 text-white bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 font-medium rounded-full text-sm px-5 py-3 text-center me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 ${isSubmitting || addDriverMutation.isPending ? 'opacity-75 cursor-not-allowed' : ''
                                                            }`}
                                                    >
                                                        {isSubmitting || addDriverMutation.isPending ? (
                                                            <>
                                                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                                </svg>
                                                                Adding Driver...
                                                            </>
                                                        ) : (
                                                            'Add New Driver'
                                                        )}
                                                    </button>
                                                ) : null}
                                            </div>
                                        </div>
                                    </DialogPanel>
                                </div>
                            </div>
                        </div>
                    </Dialog >
                </div >
            </div >
            {openApproveDocument ? <ApproveDocument open={openApproveDocument} setOpen={setOpenApproveDocument} /> : null
            }
        </>
    );
}