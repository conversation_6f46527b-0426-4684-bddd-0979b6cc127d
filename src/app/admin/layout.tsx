"use client";
import { HeaderTitleProvider } from '@/context/HeaderTitleContext'
import { useSidebar } from '@/context/SidebarContext';
import AppHeader from '@/layout/AppHeader';
import AppSidebar from '@/layout/AppSidebar'
import Backdrop from '@/layout/Backdrop'
import { usePathname } from 'next/navigation';
import React, { ReactNode } from 'react'

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }:AdminLayoutProps) {
      const pathname = usePathname();
     const { isExpanded, isHovered, isMobileOpen } = useSidebar();

     const
    mainContentMargin = isMobileOpen
      ? "ml-0"
      : isExpanded || isHovered
        ? "lg:ml-[235px]"
        : "lg:ml-[90px]",
    mainContent = "mx-auto max-w-screen-2xl bg-white mb-[15%] sm:mb-[5%]"
    + ((pathname != "/driver/live" && pathname != "/driver/start-ride") ? " p-4" : '');
  return (
      <HeaderTitleProvider >

          <div className="min-h-screen xl:flex">
            {/* <AppHeader /> */}
            <AppSidebar />
            <Backdrop />

              <div
          className={`flex-1 transition-all duration-300 ease-in-out bg-white ${mainContentMargin}`}
        >
            <AppHeader />
          <main className={mainContent}>{children}</main>
        </div>
          </div>
        </HeaderTitleProvider>
    
  )
}
